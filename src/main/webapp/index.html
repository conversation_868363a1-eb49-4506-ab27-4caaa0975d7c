<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户信息系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="number"]:focus {
            border-color: #007bff;
            outline: none;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .example {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .example h3 {
            margin-top: 0;
            color: #495057;
        }
        .example-link {
            display: block;
            color: #007bff;
            text-decoration: none;
            margin: 5px 0;
        }
        .example-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户信息系统</h1>
        
        <form id="userForm" method="GET" action="userinfo">
            <div class="form-group">
                <label for="name">姓名：</label>
                <input type="text" id="name" name="name" placeholder="请输入您的姓名" required>
            </div>
            
            <div class="form-group">
                <label for="age">年龄：</label>
                <input type="number" id="age" name="age" placeholder="请输入您的年龄" min="0" max="150" required>
            </div>
            
            <button type="submit" class="btn">提交信息</button>
            <button type="reset" class="btn btn-secondary">重置</button>
        </form>
        
        <div class="example">
            <h3>测试示例：</h3>
            <p>您也可以直接通过以下链接测试：</p>
            <a href="userinfo?name=Tom&age=20" class="example-link">测试：Tom，20岁</a>
            <a href="userinfo?name=John&age=30" class="example-link">测试：John，30岁</a>
            <a href="userinfo?name=&age=25" class="example-link">测试：姓名为空的情况</a>
            <a href="userinfo?name=Alice&age=abc" class="example-link">测试：年龄非数字的情况</a>
            <a href="userinfo?name=Bob&age=-5" class="example-link">测试：年龄为负数的情况</a>
        </div>
    </div>

    <script>
        // 表单验证
        document.getElementById('userForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const age = document.getElementById('age').value.trim();
            
            if (!name) {
                alert('请输入姓名！');
                e.preventDefault();
                return;
            }
            
            if (!age) {
                alert('请输入年龄！');
                e.preventDefault();
                return;
            }
            
            const ageNum = parseInt(age);
            if (isNaN(ageNum) || ageNum < 0 || ageNum > 150) {
                alert('请输入有效的年龄（0-150之间的整数）！');
                e.preventDefault();
                return;
            }
        });
    </script>
</body>
</html>
