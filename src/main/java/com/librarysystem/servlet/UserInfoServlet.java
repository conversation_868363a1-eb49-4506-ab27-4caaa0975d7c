package com.librarysystem.servlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * UserInfoServlet - 处理用户信息的Servlet
 * 功能：
 * 1. 接收HTTP GET请求的name和age参数
 * 2. 验证参数有效性（姓名和年龄不能为空，年龄必须是整数）
 * 3. 输出欢迎信息或错误信息
 * 
 * 访问地址：http://localhost:8080/tm1/userinfo?name=John&age=30
 */
public class UserInfoServlet extends HttpServlet {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 处理HTTP GET请求
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // 设置响应内容类型和字符编码
        response.setContentType("text/html;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        // 获取输出流
        PrintWriter out = response.getWriter();
        
        try {
            // 获取请求参数
            String name = request.getParameter("name");
            String ageStr = request.getParameter("age");
            
            // 验证参数
            ValidationResult validation = validateParameters(name, ageStr);
            
            // 生成HTML响应
            generateHtmlResponse(out, validation, name, ageStr);
            
        } catch (Exception e) {
            // 处理异常
            handleException(out, e);
        } finally {
            out.close();
        }
    }
    
    /**
     * 验证请求参数
     * @param name 姓名参数
     * @param ageStr 年龄参数字符串
     * @return 验证结果
     */
    private ValidationResult validateParameters(String name, String ageStr) {
        ValidationResult result = new ValidationResult();
        
        // 验证姓名
        if (name == null || name.trim().isEmpty()) {
            result.setValid(false);
            result.addError("姓名不能为空");
        } else {
            result.setName(name.trim());
        }
        
        // 验证年龄
        if (ageStr == null || ageStr.trim().isEmpty()) {
            result.setValid(false);
            result.addError("年龄不能为空");
        } else {
            try {
                int age = Integer.parseInt(ageStr.trim());
                if (age < 0 || age > 150) {
                    result.setValid(false);
                    result.addError("年龄必须在0-150之间");
                } else {
                    result.setAge(age);
                }
            } catch (NumberFormatException e) {
                result.setValid(false);
                result.addError("年龄必须是有效的整数");
            }
        }
        
        return result;
    }
    
    /**
     * 生成HTML响应
     */
    private void generateHtmlResponse(PrintWriter out, ValidationResult validation, 
                                    String name, String ageStr) {
        out.println("<!DOCTYPE html>");
        out.println("<html lang='zh-CN'>");
        out.println("<head>");
        out.println("    <meta charset='UTF-8'>");
        out.println("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>");
        out.println("    <title>用户信息处理结果</title>");
        out.println("    <style>");
        out.println("        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }");
        out.println("        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }");
        out.println("        .success { color: #28a745; border: 2px solid #28a745; padding: 15px; border-radius: 5px; background-color: #d4edda; }");
        out.println("        .error { color: #dc3545; border: 2px solid #dc3545; padding: 15px; border-radius: 5px; background-color: #f8d7da; }");
        out.println("        .info { margin: 20px 0; padding: 10px; background-color: #e9ecef; border-radius: 5px; }");
        out.println("        h1 { color: #333; text-align: center; }");
        out.println("        .back-link { display: block; text-align: center; margin-top: 20px; color: #007bff; text-decoration: none; }");
        out.println("        .back-link:hover { text-decoration: underline; }");
        out.println("    </style>");
        out.println("</head>");
        out.println("<body>");
        out.println("    <div class='container'>");
        out.println("        <h1>用户信息处理结果</h1>");
        
        if (validation.isValid()) {
            // 参数有效，显示欢迎信息
            out.println("        <div class='success'>");
            out.println("            <h2>✓ 处理成功</h2>");
            out.println("            <p><strong>欢迎" + validation.getName() + "，您的年龄是" + validation.getAge() + "岁！</strong></p>");
            out.println("        </div>");
            
            out.println("        <div class='info'>");
            out.println("            <h3>接收到的参数：</h3>");
            out.println("            <p><strong>姓名：</strong>" + validation.getName() + "</p>");
            out.println("            <p><strong>年龄：</strong>" + validation.getAge() + "岁</p>");
            out.println("        </div>");
        } else {
            // 参数无效，显示错误信息
            out.println("        <div class='error'>");
            out.println("            <h2>✗ 参数验证失败</h2>");
            out.println("            <p><strong>请输入有效的姓名和年龄！</strong></p>");
            out.println("            <ul>");
            for (String error : validation.getErrors()) {
                out.println("                <li>" + error + "</li>");
            }
            out.println("            </ul>");
            out.println("        </div>");
            
            out.println("        <div class='info'>");
            out.println("            <h3>接收到的参数：</h3>");
            out.println("            <p><strong>姓名：</strong>" + (name != null ? name : "未提供") + "</p>");
            out.println("            <p><strong>年龄：</strong>" + (ageStr != null ? ageStr : "未提供") + "</p>");
            out.println("        </div>");
        }
        
        out.println("        <div class='info'>");
        out.println("            <h3>使用说明：</h3>");
        out.println("            <p>请使用以下格式访问：</p>");
        out.println("            <code>http://localhost:8080/tm1/userinfo?name=姓名&age=年龄</code>");
        out.println("            <p><strong>示例：</strong></p>");
        out.println("            <code>http://localhost:8080/tm1/userinfo?name=Tom&age=20</code>");
        out.println("        </div>");
        
        out.println("        <a href='javascript:history.back()' class='back-link'>← 返回上一页</a>");
        out.println("    </div>");
        out.println("</body>");
        out.println("</html>");
    }
    
    /**
     * 处理异常
     */
    private void handleException(PrintWriter out, Exception e) {
        out.println("<!DOCTYPE html>");
        out.println("<html><head><meta charset='UTF-8'><title>系统错误</title></head>");
        out.println("<body>");
        out.println("<h1>系统错误</h1>");
        out.println("<p>处理请求时发生错误：" + e.getMessage() + "</p>");
        out.println("</body></html>");
    }
    
    /**
     * 验证结果内部类
     */
    private static class ValidationResult {
        private boolean valid = true;
        private String name;
        private int age;
        private java.util.List<String> errors = new java.util.ArrayList<>();
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public int getAge() { return age; }
        public void setAge(int age) { this.age = age; }
        
        public java.util.List<String> getErrors() { return errors; }
        public void addError(String error) { this.errors.add(error); }
    }
}
